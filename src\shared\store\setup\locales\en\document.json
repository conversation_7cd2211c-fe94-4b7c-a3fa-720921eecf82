{"actions": {"all": "All Actions", "approve": "Approved", "bounce": "Bounced", "delete": "Deleted"}, "add": "Add", "addLabel": "Tag", "approvalChecks": {"autoApprove": "Auto Approve", "classificationConfidence": "Classification Correctness", "classificationConfidenceDescription": "We're unsure about the classification of one or more parts of this bundle.", "entityConfidence": "Field Confidence", "entityOccurrences": "Field Completeness", "entityOccurrencesDescription": "There are required fields missing in this document.", "masterdata": "Masterdata", "masterdataDescription": "Masterdata must be linked to this document.", "ocrConfidence": "Text Recognition Confidence", "page": "Page"}, "approve": "Approve", "approveApprovalChecks": "Invalid Checks", "approveCopy": "Approve Copy", "approveMasterdata": "Masterdata missing", "approveMaxOccurrences": "Too many fields", "approveMinOccurrences": "Missing fields", "approveOriginal": "Approve Original", "approved": "Approved", "approving": "Approving", "archive": {"action": "Action", "actor": "Processed by", "availableTime": "Available on", "processedOn": "Processed on", "uploadTime": "Uploaded on"}, "bounceAction": {"button": "<PERSON><PERSON><PERSON>", "description": "Bouncing a document will send it back to your company to be handled further.", "title": "Bounce document"}, "bounceReason": "<PERSON><PERSON>ce Reason", "changeType": "Change Type", "checked": "Checked", "complex": "Complex", "confLow": "This field might not be correct. ", "confidence": "Confidence", "copy": "Copy", "delete": "Delete", "deleteAllFields": "Delete all fields", "deleteSuggestion": "Delete suggestion", "deleteSuggestionDescription": " We detected that this document most likely needs to be deleted. \n\nIf this is not the case, you can assign a document type to it here.", "deletion": {"copy": "Are you sure you want to delete this copy", "deleteCopy": "Delete copy", "deleteDocument": "Delete document", "deleteOriginal": "Delete document", "extra": "This will also remove all existing not approved copies.", "fields": "This will delete all currently created fields from this document, this action can not be undone.", "metadata": "This will clear all metadata values in this document, this action can not be undone.", "original": "Are you sure you want to delete this document"}, "detection": "Detection", "dialog": {"cancel": "Cancel", "confirm": "Confirm", "discard": "Discard", "processing": "Processing...", "save": "Save", "title": "Are you sure?"}, "fields": "Fields", "finishCopies": "Finish Copies first", "help": {"labelingHelp": "Labeling help", "shortcutCreate": "Create / Delete a field", "shortcutDoc": "Next / Previous document", "shortcutDrag": "Drag the page", "shortcutEdit": "Edit selected field", "shortcutHorizontal": "Scroll horizontally in the document", "shortcutPage": "Next / Previous page", "shortcutSearch": "Toggle Search panel", "shortcutZoom": "Zoom in or out in the document", "shortcuts": "Shortcuts", "toolSimple": "Simple text selection", "toolSimpleDescription": "Used for selecting text with a specific field type.", "toolVisual": "Visual selection", "toolVisualDescription": "Used for visual selection, useful for images, signatures, sketches.", "tools": "Tools"}, "inbox": "Inbox", "invalid": "Invalid", "invalidFormat": "Field has an invalid format", "labeling": {"invalidChecks": "Some checks are not satisfied, please check before proceeding", "labelAll": "Please check all fields"}, "masterdata": {"addQuery": "Add query", "allTables": "Tables: All", "clear": "Clear", "clearAll": "Clear masterdata link", "global": "Global", "import": "Import result", "importing": "Importing", "noResults": "No results", "results": "Results", "search": "Search", "searchMasterdata": "Type or select text", "searchNofields": "No fields configured", "showLess": "Show {{value}} less", "showMore": "Show {{value}} more", "table": "Table", "tables": "Tables : {{value}} selected"}, "metadata": "<PERSON><PERSON><PERSON>", "noCategory": "No Category", "noFields": "No fields detected.", "notFound": {"by": "by", "deletedOn": "Data removed from Paperbox on", "description": "We didn't find any documents with this ID. The document might have been deleted.", "return": "Return Home", "title": "Document not found"}, "ocrLow": "We're unsure about this field. ", "original": "Original", "processing": "Processing", "sourceUser": "This field was manually added by a user.", "table": "Table", "tableSelect": {"description": "Select the masterdata tables you want to use when searching.", "title": "Masterdata Tables"}, "thumbs": {"bundletype": "Change bundle type", "delete": "Delete document", "doctype": "Change document type", "download": "Download document", "mailtype": "Change mail type", "split": "Split document", "splitNoSupport": "Splitting is not supported on this document."}, "toolbar": {"awaitingSelection": "Awaiting selection", "help": "Help", "label": "In label mode, after you label a field in the document, pressing Enter adds it to your list of fields.", "search": "In search mode, once you've labeled a field in the document, pressing En<PERSON> will instantly add it to your search query and initiate the search.", "tool": "Tool", "transformations": {"undo": "Undo Transformations", "undoDescription": "Document transformations include, deleted and/or split pages. Undoing these will reset the document to its original state.", "undoTitle": "Undo Document Transformations"}, "type": "Type", "typeOrSelect": "Type or Select text", "value": "Value", "visual": "Visual"}, "typeSwitch": {"bundlePartDescription": "This will only change the type of the currently selected part.", "bundleType": "Bundle type", "bundleTypeTitle": "Change bundle type", "changeAllButton": "Also change copies", "changeButton": "Change", "docType": "Document type", "docTypeDescription": "This will only change the type of the currently selected copy.", "docTypeDescriptionMulti": "Reprocess all selected documents with a new type.", "docTypeTitle": "Change document type", "download": "Download documents", "inbox": "Inbox", "inboxButton": "Move to Inbox", "inboxDescription": "Move a document to another inbox. \nThis action will reprocess the document, all current changes will be lost.", "inboxPending": "Moving", "inboxTitle": "Move to Inbox", "locked": "Document type is fixed and cannot be changed", "mailType": "Mail type", "mailTypeTitle": "Change mail type", "subType": "Subtype", "tagTitle": "Change Tag"}, "routing": {"accessDenied": "You don't have access to this inbox.", "documentMoved": "Document was moved. Redirecting...", "documentMovedNoNext": "Document was moved and is no longer accessible.", "documentNotFound": "Document not found or no longer available.", "invalidDocumentId": "Invalid document ID. Redirecting to document list.", "documentProcessed": "Document has been processed. Redirecting to historical view."}, "unauthorized": {"description": "You're not allowed to access this document, make sure you have access and try again.", "return": "Return Home", "title": "Not Authorized"}, "valid": "<PERSON><PERSON>", "imageError": {"failedToLoad": "Failed to load image", "failedToLoadPage": "Failed to load page {{pageNo}}", "noImageAvailable": "No image available", "checkConnection": "Please check your connection or try refreshing the page", "loadingImage": "Loading image...", "preparing": "Preparing..."}}