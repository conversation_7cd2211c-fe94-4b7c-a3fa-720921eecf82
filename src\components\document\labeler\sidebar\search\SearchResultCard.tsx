import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import { IClientMasterdataSearchResult } from '@shared/helpers/converters/masterdata-result.ts';
import {
  useGetFieldtypesQuery,
  useGetMasterDataMappingsQuery,
  useGetMetadataTypesQuery,
} from '@shared/helpers/rtk-query/firestoreApi.ts';
import { useDocumentOperations } from '@shared/hooks/useDocumentOperations';
import { useNotification } from '@shared/hooks/useNotificationBar.tsx';
import { UrlParams } from '@shared/models/generic';
import checksSlice from '@shared/store/checksSlice.ts';
import { selectImportMasterdataResultStatus } from '@shared/store/documentSlice';
import { useSelector } from '@shared/store/store';
import { useDispatch } from '@shared/store/store';
import s from '@shared/styles/component/document/sidebar/search.module.scss';
import sidebarS from '@components/document/labeler/sidebar/sidebar.module.scss';
import { DotPulse } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';

interface Props {
  result: IClientMasterdataSearchResult;
}

const SearchResultCard: React.FC<Props> = ({ result }) => {
  const { inboxId }: UrlParams = useParams();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const rowsRef = useRef();

  // Get state from Redux
  const importMasterdataResultStatus = useSelector(selectImportMasterdataResultStatus);

  // Get operations from the hook
  const { importMasterdataResult } = useDocumentOperations();
  const { showNotification } = useNotification();

  // Local error state
  const [importError, setImportError] = useState<string | null>(null);

  const entityTypes = useGetFieldtypesQuery({ inboxId }).data;
  const metadataTypes = useGetMetadataTypesQuery({ inboxId }).data;
  const masterDataMappings = useGetMasterDataMappingsQuery({ inboxId }).data;
  // const masterDataImportStatus = useSelector((state) => state.documentList.masterDataImportStatus);

  const [isExpanded, setIsExpanded] = useState(false);

  const fieldTypeName = useCallback(
    (field) => {
      if (field.mapping?.referenceType) {
        if (field.mapping.referenceType.includes('metadata')) {
          const mdResult = metadataTypes.find((e) => e.id === field.mapping.reference);
          if (mdResult) return mdResult.name;
        } else {
          const etResult = entityTypes.find((e) => e.id === field.mapping.reference);
          if (etResult) return etResult.name;
        }
      } else {
        if (field.mapping?.displayName) return field.mapping?.displayName;
      }
      return field.name;
    },
    [entityTypes, metadataTypes],
  );

  const sortedResults = useMemo(() => {
    const original = result.fields;
    const filtered = original.filter((e) => e.value);

    let sortedMapped = filtered.sort((a, b) => {
      return (
        Number(b.match) - Number(a.match) ||
        Number(b?.mapping?.isPinned || false) - Number(a?.mapping?.isPinned || false) ||
        fieldTypeName(a).localeCompare(fieldTypeName(b))
      );
    });
    sortedMapped = sortedMapped.filter((e) => {
      const mapped = metadataTypes.find((m) => m.id === e.mapping?.reference);
      return !mapped?.isHidden;
    });

    return [...sortedMapped].filter(
      (field) => field.mapping.displayName != null || field.mapping.reference != null,
    );
  }, [fieldTypeName, metadataTypes, result]);

  // Clear error when user interacts with the component
  const clearError = () => {
    if (importError) {
      setImportError(null);
    }
  };

  const handleImportResult = async () => {
    setImportError(null); // Clear any previous errors

    try {
      await importMasterdataResult(result);
      dispatch(checksSlice.actions.setActiveCheck('entity_occurrence'));
      // Show success notification
      showNotification(t('document:masterdata.importSuccess'), 'success');
    } catch (error) {
      console.error('Error importing masterdata:', error);
      const errorMessage = t('document:masterdata.importError');

      // Set inline error for display
      setImportError(errorMessage);

      // Also show notification for immediate feedback
      showNotification(errorMessage, 'error');
    }
  };

  const style = useMemo(() => {
    const el = rowsRef.current as HTMLDivElement;
    if (el && isExpanded) {
      let totalHeight = 0;
      const childArr = Array.from(el.children);
      childArr.forEach((child) => {
        totalHeight += child.clientHeight + 2;
      });
      return { maxHeight: totalHeight };
    }
    return {};
  }, [isExpanded]);

  return (
    <div className={s.card} data-testid={'masterdata-result-card'}>
      {Object.keys(masterDataMappings).length > 1 && result.index?.tableName && (
        <div className={s.header}>{result.index.tableName}</div>
      )}
      <div ref={rowsRef} style={style} className={clsx(s.rows)}>
        {sortedResults.map((field) => {
          let valueRender;
          if (typeof field.value === 'string') {
            valueRender = (
              <span className={s.value}>
                {field.highlight !== null ? (
                  // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                  <div dangerouslySetInnerHTML={{ __html: field.highlight }} />
                ) : (
                  <span className={s.value_text}>{field.value}</span>
                )}
              </span>
            );
          } else {
            const value = field.value as string[];
            const highlight = Array.isArray(field.highlight) ? field.highlight : null;
            const hasHighlightArray = Array.isArray(highlight) && highlight.length === value.length;

            if (value.length >= 2) {
              // Show only first item with "+X more" tooltip for arrays with 2+ items
              const remainingItems = value.slice(1);
              const remainingHighlights = hasHighlightArray ? highlight.slice(1) : null;

              const tooltipContent = (
                <div className={s.tooltip_content}>
                  {remainingItems.map((item, index) => (
                    <div key={index} className={s.tooltip_item}>
                      {remainingHighlights?.[index] ? (
                        // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                        <span dangerouslySetInnerHTML={{ __html: remainingHighlights[index] }} />
                      ) : (
                        <span className={s.tooltip_text}>{item}</span>
                      )}
                    </div>
                  ))}
                </div>
              );

              valueRender = (
                <div className={s.value_list}>
                  <div className={s.item} key={value[0]}>
                    {hasHighlightArray && highlight[0] ? (
                      // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                      <div dangerouslySetInnerHTML={{ __html: highlight[0] }} />
                    ) : (
                      <span className={s.value_text}>{value[0]}</span>
                    )}
                  </div>
                  <Tooltip content={tooltipContent} position="right" lightTheme={true}>
                    <div className={s.item}>
                      <span>+{remainingItems.length} more</span>
                    </div>
                  </Tooltip>
                </div>
              );
            } else {
              // Show single item normally
              valueRender = (
                <div className={s.value_list}>
                  {value.map((v, index) => (
                    <div className={s.item} key={v + index}>
                      {hasHighlightArray && highlight[index] ? (
                        // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                        <div dangerouslySetInnerHTML={{ __html: highlight[index] }} />
                      ) : (
                        <span className={s.value_text}>{v}</span>
                      )}
                    </div>
                  ))}
                </div>
              );
            }
          }
          return (
            <div
              key={field.value + field.name}
              className={clsx(
                s.row,
                { [s.row__matched]: field.match },
                { [s.row__field]: field.mapping?.referenceType },
              )}
            >
              <div className={s.info}>
                {valueRender}
                <span className={s.type}>{fieldTypeName(field)}</span>
              </div>
              {field.mapping?.referenceType && <div className={s.tag} />}
            </div>
          );
        })}
      </div>
      <div className={s.card_overlay}>
        <div
          data-testid={'masterdata-import'}
          onClick={handleImportResult}
          className={s.card_overlay_item}
          onFocus={clearError} // Clear error when user focuses on button
        >
          {importMasterdataResultStatus === 'importing' ? (
            <>
              <div className={s.card_overlay_icon}>
                <DotPulse size={18} color={'#0085FF'} />
              </div>
            </>
          ) : (
            <>
              <span>{t('document:masterdata.import')}</span>
            </>
          )}
        </div>
        {sortedResults.length > 6 && (
          <div onClick={() => setIsExpanded(!isExpanded)} className={s.card_overlay_item}>
            {!isExpanded ? (
              <span>{t('document:masterdata.showMore', { value: sortedResults.length - 6 })}</span>
            ) : (
              <span>{t('document:masterdata.showLess', { value: sortedResults.length - 6 })}</span>
            )}
          </div>
        )}
      </div>
      {importError && (
        <div className={sidebarS.error_message}>
          <div className={sidebarS.error_icon}>⚠️</div>
          <div className={sidebarS.error_text}>{importError}</div>
          <button className={sidebarS.error_dismiss} onClick={clearError} aria-label="Dismiss error">
            ×
          </button>
        </div>
      )}
      {!isExpanded && sortedResults.length > 6 && <div className={s.overflow} />}
    </div>
  );
};

export default SearchResultCard;
